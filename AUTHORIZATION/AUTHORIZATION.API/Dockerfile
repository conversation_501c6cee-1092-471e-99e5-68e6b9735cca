FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy project files
COPY ["AUTHORIZATION/AUTHORIZATION.API/AUTHORIZATION.API.csproj", "AUTHORIZATION/AUTHORIZATION.API/"]
COPY ["E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/ServiceDefaults.csproj", "E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/"]

# Restore dependencies
RUN dotnet restore "AUTHORIZATION/AUTHORIZATION.API/AUTHORIZATION.API.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/AUTHORIZATION/AUTHORIZATION.API"
RUN dotnet build "AUTHORIZATION.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "AUTHORIZATION.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "AUTHORIZATION.API.dll"]
