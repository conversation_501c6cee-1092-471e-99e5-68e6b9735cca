FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy project files
COPY ["E_COMMERCE_TEDDYBEAR_SHOP.AppHost/AppHost.csproj", "E_COMMERCE_TEDDYBEAR_SHOP.AppHost/"]
COPY ["AUTHORIZATION/AUTHORIZATION.API/AUTHORIZATION.API.csproj", "AUTHORIZATION/AUTHORIZATION.API/"]
COPY ["COMMAND/COMMAND.API/COMMAND.API.csproj", "COMMAND/COMMAND.API/"]
COPY ["QUERY/QUERY.API/QUERY.API.csproj", "QUERY/QUERY.API/"]
COPY ["E_COMMERCE_TEDDY<PERSON><PERSON>_SHOP.ServiceDefaults/ServiceDefaults.csproj", "E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/"]

# Copy all other project dependencies
COPY ["COMMAND/COMMAND.APPLICATION/COMMAND.APPLICATION.csproj", "COMMAND/COMMAND.APPLICATION/"]
COPY ["COMMAND/COMMAND.CONTRACT/COMMAND.CONTRACT.csproj", "COMMAND/COMMAND.CONTRACT/"]
COPY ["COMMAND/COMMAND.INFRASTRUCTURE/COMMAND.INFRASTRUCTURE.csproj", "COMMAND/COMMAND.INFRASTRUCTURE/"]
COPY ["COMMAND/COMMAND.PERSISTENCE/COMMAND.PERSISTENCE.csproj", "COMMAND/COMMAND.PERSISTENCE/"]
COPY ["COMMAND/COMMAND.PRESENTATION/COMMAND.PRESENTATION.csproj", "COMMAND/COMMAND.PRESENTATION/"]
COPY ["QUERY/QUERY.APPLICATION/QUERY.APPLICATION.csproj", "QUERY/QUERY.APPLICATION/"]
COPY ["QUERY/QUERY.CONTRACT/QUERY.CONTRACT.csproj", "QUERY/QUERY.CONTRACT/"]
COPY ["QUERY/QUERY.INFRASTRUCTURE/QUERY.INFRASTRUCTURE.csproj", "QUERY/QUERY.INFRASTRUCTURE/"]
COPY ["QUERY/QUERY.PERSISTENCE/QUERY.PERSISTENCE.csproj", "QUERY/QUERY.PERSISTENCE/"]
COPY ["QUERY/QUERY.PRESENTATION/QUERY.PRESENTATION.csproj", "QUERY/QUERY.PRESENTATION/"]
COPY ["CONTRACT/CONTRACT/CONTRACT.csproj", "CONTRACT/CONTRACT/"]

# Restore dependencies
RUN dotnet restore "E_COMMERCE_TEDDYBEAR_SHOP.AppHost/AppHost.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/E_COMMERCE_TEDDYBEAR_SHOP.AppHost"
RUN dotnet build "AppHost.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "AppHost.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "AppHost.dll"]
