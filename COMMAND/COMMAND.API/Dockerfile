FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy project files
COPY ["COMMAND/COMMAND.API/COMMAND.API.csproj", "COMMAND/COMMAND.API/"]
COPY ["COMMAND/COMMAND.APPLICATION/COMMAND.APPLICATION.csproj", "COMMAND/COMMAND.APPLICATION/"]
COPY ["COMMAND/COMMAND.CONTRACT/COMMAND.CONTRACT.csproj", "COMMAND/COMMAND.CONTRACT/"]
COPY ["COMMAND/COMMAND.INFRASTRUCTURE/COMMAND.INFRASTRUCTURE.csproj", "COMMAND/COMMAND.INFRASTRUCTURE/"]
COPY ["COMMAND/COMMAND.PERSISTENCE/COMMAND.PERSISTENCE.csproj", "COMMAND/COMMAND.PERSISTENCE/"]
COPY ["COMMAND/COMMAND.PRESENTATION/COMMAND.PRESENTATION.csproj", "COMMAND/COMMAND.PRESENTATION/"]
COPY ["CONTRACT/CONTRACT/CONTRACT.csproj", "CONTRACT/CONTRACT/"]
COPY ["E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/ServiceDefaults.csproj", "E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/"]

# Restore dependencies
RUN dotnet restore "COMMAND/COMMAND.API/COMMAND.API.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/COMMAND/COMMAND.API"
RUN dotnet build "COMMAND.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "COMMAND.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "COMMAND.API.dll"]
